--cpu Cortex-M3
"gk_f103ve_1.0_dome\startup_stm32f103xe.o"
"gk_f103ve_1.0_dome\main.o"
"gk_f103ve_1.0_dome\gpio.o"
"gk_f103ve_1.0_dome\dac.o"
"gk_f103ve_1.0_dome\dma.o"
"gk_f103ve_1.0_dome\fsmc.o"
"gk_f103ve_1.0_dome\i2c.o"
"gk_f103ve_1.0_dome\sdio.o"
"gk_f103ve_1.0_dome\spi.o"
"gk_f103ve_1.0_dome\tim.o"
"gk_f103ve_1.0_dome\usart.o"
"gk_f103ve_1.0_dome\stm32f1xx_it.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_msp.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_gpio_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_pcd.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_pcd_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_ll_usb.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_rcc.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_rcc_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_gpio.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_dma.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_cortex.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_pwr.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_flash.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_flash_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_exti.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_dac.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_dac_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_ll_fsmc.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_sram.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_i2c.o"
"gk_f103ve_1.0_dome\stm32f1xx_ll_sdmmc.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_sd.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_spi.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_tim.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_tim_ex.o"
"gk_f103ve_1.0_dome\stm32f1xx_hal_uart.o"
"gk_f103ve_1.0_dome\system_stm32f1xx.o"
"gk_f103ve_1.0_dome\ws_drive_borad.o"
"gk_f103ve_1.0_dome\ws_drive_tft.o"
"gk_f103ve_1.0_dome\ws_drive_dwt.o"
"gk_f103ve_1.0_dome\kernel.o"
"gk_f103ve_1.0_dome\ws_drive_mpu6050.o"
"gk_f103ve_1.0_dome\ws_drive_oled.o"
"gk_f103ve_1.0_dome\ws_core.o"
"gk_f103ve_1.0_dome\ws_drive_w25qxx.o"
"gk_f103ve_1.0_dome\ws_config.o"
"gk_f103ve_1.0_dome\ws_app_w25qxx.o"
"gk_f103ve_1.0_dome\ws_app_sd.o"
"gk_f103ve_1.0_dome\bsp_driver_sd.o"
"gk_f103ve_1.0_dome\fatfs.o"
"gk_f103ve_1.0_dome\diskio.o"
"gk_f103ve_1.0_dome\ff.o"
"gk_f103ve_1.0_dome\ff_gen_drv.o"
"gk_f103ve_1.0_dome\syscall.o"
"gk_f103ve_1.0_dome\sd_diskio.o"
"gk_f103ve_1.0_dome\usb_device.o"
"gk_f103ve_1.0_dome\usbd_desc.o"
"gk_f103ve_1.0_dome\usbd_storage_if.o"
"gk_f103ve_1.0_dome\usbd_conf.o"
"gk_f103ve_1.0_dome\usbd_core.o"
"gk_f103ve_1.0_dome\usbd_ctlreq.o"
"gk_f103ve_1.0_dome\usbd_ioreq.o"
"gk_f103ve_1.0_dome\usbd_msc.o"
"gk_f103ve_1.0_dome\usbd_msc_bot.o"
"gk_f103ve_1.0_dome\usbd_msc_data.o"
"gk_f103ve_1.0_dome\usbd_msc_scsi.o"
--strict --scatter "GK_F103VE_1.0_DOME\GK_F103VE_1.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GK_F103VE_1.map" -o GK_F103VE_1.0_DOME\GK_F103VE_1.0_DOME
Dependencies for Project 'GK_F103VE_1.0_DOME', Target 'GK_F103VE_1.0_DOME': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (startup_stm32f103xe.s)(0x64B60D6E)(--cpu Cortex-M3 -g --diag_suppress=A1950W

-I.\RTE\_GK_F103VE_1.0_DOME

-IC:\Keil_v5\ARM\CMSIS\5.4.0\CMSIS\Core\Include

-IC:\Keil_v5\Keil\STM32F1xx_DFP\2.4.1\Device\Include

--pd "__UVISION_VERSION SETA 541"

--pd "STM32F10X_HD SETA 1"

--pd "_RTE_ SETA 1"

--list startup_stm32f103xe.lst

--xref -o gk_f103ve_1.0_dome\startup_stm32f103xe.o

--depend gk_f103ve_1.0_dome\startup_stm32f103xe.d)
F (../Core/Src/main.c)(0x64B60A05)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/main.o -MMD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Core\Inc\dma.h)(0x647C96DC)
I (..\FATFS\App\fatfs.h)(0x64A80DEC)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\drivers\sd_diskio.h)(0x60DB1DB5)
I (..\Core\Inc\i2c.h)(0x647C96DC)
I (..\Core\Inc\sdio.h)(0x647C96DD)
I (..\Core\Inc\spi.h)(0x647C96DD)
I (..\Core\Inc\usart.h)(0x64A1260E)
I (..\USB_DEVICE\App\usb_device.h)(0x64A18F84)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\gpio.h)(0x647C96DA)
I (..\Core\Inc\fsmc.h)(0x647C96DC)
F (../Core/Src/gpio.c)(0x64A19ECC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x647C96DA)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/dac.c)(0x64B60D68)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/dac.o -MMD)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/dma.c)(0x64B60A02)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/dma.o -MMD)
I (..\Core\Inc\dma.h)(0x647C96DC)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/fsmc.c)(0x647C96DC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/fsmc.o -MMD)
I (..\Core\Inc\fsmc.h)(0x647C96DC)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/i2c.c)(0x64A19ED0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/i2c.o -MMD)
I (..\Core\Inc\i2c.h)(0x647C96DC)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/sdio.c)(0x64A19ED0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/sdio.o -MMD)
I (..\Core\Inc\sdio.h)(0x647C96DD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/spi.c)(0x64A77AA2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/spi.o -MMD)
I (..\Core\Inc\spi.h)(0x647C96DD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/tim.c)(0x64B60B2A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
F (../Core/Src/usart.c)(0x64A19ED0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usart.o -MMD)
I (..\Core\Inc\usart.h)(0x64A1260E)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Core/Src/stm32f1xx_it.c)(0x64B60A04)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Core\Inc\stm32f1xx_it.h)(0x64B60A04)
F (../Core/Src/stm32f1xx_hal_msp.c)(0x647C96DE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_gpio_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_pcd.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_pcd_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_ll_usb.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_ll_usb.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_dma.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_flash.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_exti.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dac.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_dac.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dac_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_dac_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_ll_fsmc.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_ll_fsmc.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_sram.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_sram.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_i2c.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_ll_sdmmc.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_ll_sdmmc.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_sd.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_sd.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_spi.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_tim.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/stm32f1xx_hal_uart.o -MMD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../Core/Src/system_stm32f1xx.c)(0x60DB1DC5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/system_stm32f1xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (..\WS-Drivers\ws_drive_borad.c)(0x64B60EE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_borad.o -MMD)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\App\fatfs.h)(0x64A80DEC)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\drivers\sd_diskio.h)(0x60DB1DB5)
F (..\WS-Drivers\ws_drive_tft.c)(0x6483E40C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_tft.o -MMD)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\WS-Drivers\drive_dzcode.h)(0x62D806BD)
F (..\WS-Drivers\ws_drive_dwt.c)(0x644A4857)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_dwt.o -MMD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (..\WSOS\kernel.c)(0x6461ED16)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/kernel.o -MMD)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (..\WS-Drivers\ws_drive_mpu6050.c)(0x6468DB5C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_mpu6050.o -MMD)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (..\WS-Drivers\ws_drive_oled.c)(0x646A1D8A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_oled.o -MMD)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\WS-Drivers\ws_oled_font.h)(0x646A17C3)
F (..\WS-Drivers\ws_core.c)(0x64A13299)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_core.o -MMD)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Core\Inc\usart.h)(0x64A1260E)
F (..\WS-Drivers\ws_drive_w25qxx.c)(0x64A8180F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_drive_w25qxx.o -MMD)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (..\WS-Drivers\ws_config.c)(0x64A151B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_config.o -MMD)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (..\WS-App\ws_app_w25qxx.c)(0x64A81A46)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_app_w25qxx.o -MMD)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\App\fatfs.h)(0x64A80DEC)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\drivers\sd_diskio.h)(0x60DB1DB5)
F (..\WS-App\ws_app_sd.c)(0x64A819F3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ws_app_sd.o -MMD)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\App\fatfs.h)(0x64A80DEC)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\drivers\sd_diskio.h)(0x60DB1DB5)
F (../FATFS/Target/bsp_driver_sd.c)(0x64809D81)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/bsp_driver_sd.o -MMD)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
F (../FATFS/App/fatfs.c)(0x64A80D6B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/fatfs.o -MMD)
I (..\Core\Inc\sdio.h)(0x647C96DD)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\App\fatfs.h)(0x64A80DEC)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\drivers\sd_diskio.h)(0x60DB1DB5)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ff.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/ff_gen_drv.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
F (../Middlewares/Third_Party/FatFs/src/option/syscall.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/syscall.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
F (../Middlewares/Third_Party/FatFs/src/drivers/sd_diskio.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/sd_diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x60DB1DB5)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x60DB1DB5)
I (..\FATFS\Target\ffconf.h)(0x64809D81)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
F (../USB_DEVICE/App/usb_device.c)(0x64A18F84)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usb_device.o -MMD)
I (..\USB_DEVICE\App\usb_device.h)(0x64A18F84)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\USB_DEVICE\App\usbd_desc.h)(0x64A18F85)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
I (..\USB_DEVICE\App\usbd_storage_if.h)(0x64A18F85)
F (../USB_DEVICE/App/usbd_desc.c)(0x64A18F85)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_desc.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\USB_DEVICE\App\usbd_desc.h)(0x64A18F85)
F (../USB_DEVICE/App/usbd_storage_if.c)(0x64A81145)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_storage_if.o -MMD)
I (..\USB_DEVICE\App\usbd_storage_if.h)(0x64A18F85)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
I (..\FATFS\Target\bsp_driver_sd.h)(0x64809D81)
I (..\Core\Inc\sdio.h)(0x647C96DD)
F (../USB_DEVICE/Target/usbd_conf.c)(0x64A19ED1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_conf.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_core.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_ctlreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_ioreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Src/usbd_msc.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_msc.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Src/usbd_msc_bot.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_msc_bot.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
F (../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Src/usbd_msc_data.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_msc_data.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_data.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
F (../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Src/usbd_msc_scsi.c)(0x60DB1DB5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ../WS-Drivers -I ../WSOS -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../Middlewares/Third_Party/FatFs/src/drivers -I ../USB_DEVICE/App -I ../USB_DEVICE/Target -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/MSC/Inc -I ../WS-App

-I./RTE/_GK_F103VE_1.0_DOME

-IC:/Keil_v5/ARM/CMSIS/5.4.0/CMSIS/Core/Include

-IC:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="541" -DSTM32F10X_HD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xE

-o gk_f103ve_1.0_dome/usbd_msc_scsi.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_bot.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x60DB1DB5)
I (..\USB_DEVICE\Target\usbd_conf.h)(0x64A19AEB)
I (..\Core\Inc\main.h)(0x64B60EE6)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x60DB1DC5)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x64B60D6B)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h)(0x60DB1DC5)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x60DB1DB4)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dac_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sram.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_fsmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_sd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_sdmmc.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_ll_usb.h)(0x60DB1DC5)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pcd_ex.h)(0x60DB1DC5)
I (..\WS-Drivers\ws_drive_borad.h)(0x64B5FDBA)
I (..\WS-Drivers\ws_drive_tft.h)(0x648978FD)
I (..\WS-Drivers\ws_drive_dwt.h)(0x6445F11C)
I (..\WS-Drivers\ws_core.h)(0x64A11998)
I (..\WS-Drivers\ws_drive_mpu6050.h)(0x6462D6C6)
I (..\WS-Drivers\ws_drive_oled.h)(0x646A1DDE)
I (..\WS-Drivers\ws_drive_w25qxx.h)(0x64A807AE)
I (..\WS-Drivers\ws_config.h)(0x64A14CC3)
I (..\WSOS\kernel.h)(0x6461ED16)
I (..\WS-App\ws_app_w25qxx.h)(0x64A77CDC)
I (..\WS-App\ws_app_sd.h)(0x64A80D6B)
I (..\Core\Inc\dac.h)(0x64B5F779)
I (..\Core\Inc\tim.h)(0x64B60A03)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_scsi.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc.h)(0x60DB1DB5)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\MSC\Inc\usbd_msc_data.h)(0x60DB1DB5)
